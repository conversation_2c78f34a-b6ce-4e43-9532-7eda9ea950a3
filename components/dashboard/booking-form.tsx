"use client"

import { useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { BookingFormData, BookingRequest, Hotel, RoomType } from "@/types/accommodation"
import { getAuthToken } from "@/utils/auth"

interface BookingFormProps {
  hotel: Hotel
  roomType: RoomType
  onBookingSuccess: () => void
  onCancel: () => void
}

export default function BookingForm({ hotel, roomType, onBookingSuccess, onCancel }: BookingFormProps) {
  const { user } = useUser()
  const { addToast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isValidatingUsername, setIsValidatingUsername] = useState(false)

  // Form data state
  const [formData, setFormData] = useState<BookingFormData>({
    checkinDate: hotel.defaultCheckinDate ? hotel.defaultCheckinDate.split("T")[0] || "2025-10-16" : "2025-10-16",
    checkoutDate: hotel.defaultCheckoutDate ? hotel.defaultCheckoutDate.split("T")[0] || "2025-10-19" : "2025-10-19",
    sharedOptions: "none",
    partnerUsername: "",
    isTeamBooking: false,
    roomCount: 1,
  })

  // Calculate total price
  const calculateTotalPrice = () => {
    if (!formData.checkinDate || !formData.checkoutDate) return roomType.price
    const checkIn = new Date(formData.checkinDate)
    const checkOut = new Date(formData.checkoutDate)
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
    const roomMultiplier = formData.isTeamBooking ? formData.roomCount : 1
    return roomType.price * Math.max(1, nights) * roomMultiplier
  }

  // Calculate number of nights
  const calculateNights = () => {
    if (!formData.checkinDate || !formData.checkoutDate) return 1
    const checkIn = new Date(formData.checkinDate)
    const checkOut = new Date(formData.checkoutDate)
    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
  }

  // Validate partner username and check reservation status
  const validatePartnerUsername = async (username: string): Promise<{ isValid: boolean; userId?: number }> => {
    if (!username.trim()) return { isValid: false }

    setIsValidatingUsername(true)
    try {
      const authToken = getAuthToken()
      if (!authToken) {
        return { isValid: false }
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/accommodations/${encodeURIComponent(username.trim())}/status`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      })

      const result = (await response.json()) as {
        code: number
        data?: { user_id: number }
        msg?: string
      }

      if (result.code === 200 && result.data) {
        return {
          isValid: true,
          userId: result.data.user_id,
        }
      }

      return { isValid: false }
    } catch (error) {
      console.error("Partner validation error:", error)
      return { isValid: false }
    } finally {
      setIsValidatingUsername(false)
    }
  }

  // Validate partner username when shared option is "with_partner"
  const validatePartner = async (): Promise<{ isValid: boolean; partnerUserId?: number }> => {
    if (formData.sharedOptions === "with_partner") {
      if (!formData.partnerUsername.trim()) {
        addToast({
          type: "error",
          title: "Partner Required",
          message: "Please enter your partner's username",
        })
        return { isValid: false }
      }

      const validation = await validatePartnerUsername(formData.partnerUsername.trim())
      if (!validation.isValid) {
        addToast({
          type: "error",
          title: "Partner Not Available",
          message: "The partner username does not exist or is not available for room sharing",
        })
        return { isValid: false }
      }

      return { isValid: true, partnerUserId: validation.userId }
    }
    return { isValid: true }
  }

  // Submit booking
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!formData.checkinDate || !formData.checkoutDate) {
      addToast({
        type: "error",
        title: "Invalid Dates",
        message: "Please select both check-in and check-out dates",
      })
      return
    }

    if (new Date(formData.checkinDate) >= new Date(formData.checkoutDate)) {
      addToast({
        type: "error",
        title: "Invalid Dates",
        message: "Check-out date must be after check-in date",
      })
      return
    }

    // Validate partner if needed
    const partnerValidation = await validatePartner()
    if (!partnerValidation.isValid) {
      return
    }

    if (!user?.id) {
      addToast({
        type: "error",
        title: "Authentication Error",
        message: "Please login again",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const authToken = getAuthToken()
      if (!authToken) {
        addToast({
          type: "error",
          title: "Authentication Error",
          message: "Please login again",
        })
        return
      }

      // Convert UI form data to API format
      const sharedOptionMap = {
        none: 1,
        with_partner: 2,
        system_assigned: 3,
      } as const

      const bookingData: BookingRequest = {
        room_id: parseInt(roomType.id) || 1, // Use room_id directly from API
        shared_option: sharedOptionMap[formData.sharedOptions],
        occupant: parseInt(user.id),
        checkin_date: formData.checkinDate,
        checkout_date: formData.checkoutDate,
        is_assigned: formData.sharedOptions === "system_assigned",
        partner_user_id: partnerValidation.partnerUserId,
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/accommodations/submit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify(bookingData),
      })

      const result = (await response.json()) as { code: number; msg?: string; data?: unknown }

      if (result.code === 200) {
        addToast({
          type: "success",
          title: "Booking Successful",
          message: result.msg || "Your accommodation booking has been submitted successfully.",
        })
        onBookingSuccess()
      } else {
        addToast({
          type: "error",
          title: "Booking Failed",
          message: result.msg || "Failed to submit your booking. Please try again.",
        })
      }
    } catch (error) {
      console.error("Booking submission error:", error)
      addToast({
        type: "error",
        title: "Booking Failed",
        message: "An error occurred while submitting your booking. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Book Your Stay</CardTitle>
        <CardDescription>
          Complete your booking for {hotel.name} - {roomType.type}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Hotel and room type information */}
          <div className="rounded-lg bg-gray-50 p-4">
            <h4 className="font-medium text-gray-900">{hotel.name}</h4>
            <p className="text-sm text-gray-600">{roomType.type}</p>
            <p className="text-lg font-bold text-gray-900">¥{roomType.price}/night</p>
          </div>

          {/* Team booking option */}
          <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <Label className="text-base font-semibold text-gray-900">Booking Type *</Label>
            <div className="mt-3 grid grid-cols-2 gap-4">
              <div
                className={`cursor-pointer rounded-lg border-2 p-3 transition-all ${
                  !formData.isTeamBooking
                    ? "border-gray-500 bg-gray-50"
                    : "border-gray-200 bg-white hover:border-gray-300"
                }`}
                onClick={() => setFormData({ ...formData, isTeamBooking: false, roomCount: 1, sharedOptions: "none" })}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    id="individual"
                    name="bookingType"
                    checked={!formData.isTeamBooking}
                    onChange={() =>
                      setFormData({ ...formData, isTeamBooking: false, roomCount: 1, sharedOptions: "none" })
                    }
                    className="h-4 w-4 text-gray-600"
                  />
                  <div>
                    <Label htmlFor="individual" className="cursor-pointer font-medium text-gray-900">
                      Individual Booking
                    </Label>
                    <p className="text-sm text-gray-500">Book a single room</p>
                  </div>
                </div>
              </div>
              <div className="cursor-not-allowed rounded-lg border-2 border-gray-200 bg-gray-100 p-3 opacity-50">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    id="team"
                    name="bookingType"
                    checked={false}
                    disabled={true}
                    className="h-4 w-4 text-gray-400"
                  />
                  <div>
                    <Label htmlFor="team" className="cursor-not-allowed font-medium text-gray-400">
                      Team Booking (Coming Soon)
                    </Label>
                    <p className="text-sm text-gray-400">Book multiple rooms</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Room count selection for team booking */}
            {formData.isTeamBooking && (
              <div className="mt-4">
                <Label htmlFor="roomCount">Number of Rooms *</Label>
                <select
                  id="roomCount"
                  value={formData.roomCount}
                  onChange={(e) => setFormData({ ...formData, roomCount: parseInt(e.target.value) })}
                  className="mt-1 block w-32 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-gray-500 focus:ring-1 focus:ring-gray-500 focus:outline-none"
                  required
                >
                  <option value={1}>1 Room</option>
                  <option value={2}>2 Rooms</option>
                  <option value={3}>3 Rooms</option>
                  <option value={4}>4 Rooms</option>
                  <option value={5}>5 Rooms</option>
                </select>
                <p className="mt-1 text-sm text-gray-500">Maximum 5 rooms per team booking</p>
              </div>
            )}
          </div>

          {/* Check-in and check-out dates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="checkinDate">Check-in Date *</Label>
              <Input
                id="checkinDate"
                type="date"
                value={formData.checkinDate}
                onChange={(e) => setFormData({ ...formData, checkinDate: e.target.value })}
                min="2025-10-15"
                max="2025-10-20"
                required
              />
            </div>
            <div>
              <Label htmlFor="checkoutDate">Check-out Date *</Label>
              <Input
                id="checkoutDate"
                type="date"
                value={formData.checkoutDate}
                onChange={(e) => setFormData({ ...formData, checkoutDate: e.target.value })}
                min="2025-10-16"
                max="2025-10-21"
                required
              />
            </div>
          </div>

          {/* Shared room options */}
          <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-semibold text-gray-900">Room Sharing Options *</Label>
              {formData.isTeamBooking && (
                <span className="rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800">
                  Flexible Room Assignment
                </span>
              )}
            </div>
            <div className="mt-4 space-y-3">
              <div
                className={`cursor-pointer rounded-lg border-2 p-3 transition-all ${
                  formData.sharedOptions === "none"
                    ? "border-gray-500 bg-gray-50"
                    : "border-gray-200 bg-white hover:border-gray-300"
                }`}
                onClick={() => setFormData({ ...formData, sharedOptions: "none" })}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    id="none"
                    name="sharedOptions"
                    value="none"
                    checked={formData.sharedOptions === "none"}
                    onChange={(e) =>
                      setFormData({ ...formData, sharedOptions: e.target.value as BookingFormData["sharedOptions"] })
                    }
                    className="h-4 w-4 text-gray-600"
                  />
                  <div>
                    <Label htmlFor="none" className="cursor-pointer font-medium text-gray-900">
                      No sharing (single occupancy)
                    </Label>
                    <p className="text-sm text-gray-500">Private room for yourself</p>
                  </div>
                </div>
              </div>

              <div
                className={`cursor-pointer rounded-lg border-2 p-3 transition-all ${
                  formData.isTeamBooking
                    ? "border-gray-200 bg-gray-100 opacity-50"
                    : formData.sharedOptions === "with_partner"
                      ? "border-gray-500 bg-gray-50"
                      : "border-gray-200 bg-white hover:border-gray-300"
                }`}
                onClick={() => !formData.isTeamBooking && setFormData({ ...formData, sharedOptions: "with_partner" })}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    id="with_partner"
                    name="sharedOptions"
                    value="with_partner"
                    checked={formData.sharedOptions === "with_partner"}
                    disabled={formData.isTeamBooking}
                    onChange={(e) =>
                      setFormData({ ...formData, sharedOptions: e.target.value as BookingFormData["sharedOptions"] })
                    }
                    className="h-4 w-4 text-gray-600"
                  />
                  <div>
                    <Label
                      htmlFor="with_partner"
                      className={`cursor-pointer font-medium ${formData.isTeamBooking ? "text-gray-400" : "text-gray-900"}`}
                    >
                      Share with partner
                    </Label>
                    <p className={`text-sm ${formData.isTeamBooking ? "text-gray-400" : "text-gray-500"}`}>
                      Share room with a specific person
                    </p>
                  </div>
                </div>
              </div>

              <div
                className={`cursor-pointer rounded-lg border-2 p-3 transition-all ${
                  formData.isTeamBooking
                    ? "border-gray-200 bg-gray-100 opacity-50"
                    : formData.sharedOptions === "system_assigned"
                      ? "border-gray-500 bg-gray-50"
                      : "border-gray-200 bg-white hover:border-gray-300"
                }`}
                onClick={() =>
                  !formData.isTeamBooking && setFormData({ ...formData, sharedOptions: "system_assigned" })
                }
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    id="system_assigned"
                    name="sharedOptions"
                    value="system_assigned"
                    checked={formData.sharedOptions === "system_assigned"}
                    disabled={formData.isTeamBooking}
                    onChange={(e) =>
                      setFormData({ ...formData, sharedOptions: e.target.value as BookingFormData["sharedOptions"] })
                    }
                    className="h-4 w-4 text-gray-600"
                  />
                  <div>
                    <Label
                      htmlFor="system_assigned"
                      className={`cursor-pointer font-medium ${formData.isTeamBooking ? "text-gray-400" : "text-gray-900"}`}
                    >
                      System assigned roommate
                    </Label>
                    <p className={`text-sm ${formData.isTeamBooking ? "text-gray-400" : "text-gray-500"}`}>
                      Let us find a suitable roommate
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Partner username input */}
            {formData.sharedOptions === "with_partner" && (
              <div className="mt-4">
                <Label htmlFor="partnerUsername">Partner Username *</Label>
                <Input
                  id="partnerUsername"
                  placeholder="Enter your partner's username"
                  value={formData.partnerUsername}
                  onChange={(e) => setFormData({ ...formData, partnerUsername: e.target.value })}
                  required
                />
                {isValidatingUsername && <p className="mt-1 text-sm text-gray-500">Validating username...</p>}
              </div>
            )}

            {/* System assigned notice */}
            {formData.sharedOptions === "system_assigned" && (
              <div className="mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                <div className="flex items-start">
                  <i className="fas fa-info-circle mt-0.5 mr-2 text-yellow-600"></i>
                  <div className="text-sm text-yellow-800">
                    <p className="mb-1 font-medium">System Assignment Notice</p>
                    <p>
                      Your booking will only be successful if we can find a suitable roommate for you. We will notify
                      you via email once a roommate has been assigned.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Price summary */}
          <div className="rounded-lg border-2 border-green-200 bg-green-50 p-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-green-900">
                    {formData.isTeamBooking
                      ? `Total Cost (${formData.roomCount} rooms × ${calculateNights()} nights)`
                      : `Total Cost (${calculateNights()} nights)`}
                  </h4>
                  <p className="text-sm text-green-700">Conference special rate</p>
                </div>
                <div className="text-right">
                  <span className="text-2xl font-bold text-green-900">¥{calculateTotalPrice()}</span>
                </div>
              </div>
              {formData.isTeamBooking && (
                <div className="border-t border-green-200 pt-3">
                  <div className="flex items-center justify-between text-sm text-green-700">
                    <span>Per room per night:</span>
                    <span className="font-medium">¥{roomType.price}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-green-700">
                    <span>Number of rooms:</span>
                    <span className="font-medium">{formData.roomCount}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-green-700">
                    <span>Number of nights:</span>
                    <span className="font-medium">{calculateNights()}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-3 pt-2">
            <Button type="button" variant="outline" onClick={onCancel} className="flex-1" size="sm">
              <i className="fas fa-arrow-left mr-2"></i>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="flex-1" size="sm">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Processing...
                </>
              ) : (
                <>
                  <i className="fas fa-check mr-2"></i>
                  Confirm Booking
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
